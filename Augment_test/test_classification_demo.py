#!/usr/bin/env python3
"""
Demo script to test the classification functionality.

This script demonstrates how to use the test_classification.py script
with different parameters and scenarios.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts'))

from test_classification import FileProcessor


async def demo_classification():
    """Demonstrate the classification functionality."""
    
    print("🧪 Classification Test Demo")
    print("=" * 50)
    
    # Test folder path
    test_folder = "/home/<USER>/Desktop/test_ligistically/input_data/1_bol"
    
    # Check if test folder exists
    if not os.path.exists(test_folder):
        print(f"❌ Test folder not found: {test_folder}")
        print("Please ensure the input_data folder exists with some test files.")
        return
    
    # Initialize processor
    processor = FileProcessor("output")
    
    # Get list of files to process
    files = processor.get_supported_files(test_folder)
    print(f"📁 Found {len(files)} supported files in {test_folder}")
    
    if not files:
        print("⚠ No supported files found. Please add some PDF, JPG, PNG, or TIFF files to test.")
        return
    
    # Show first few files
    print("\n📄 Sample files:")
    for i, file_path in enumerate(files[:5]):
        print(f"   {i+1}. {Path(file_path).name}")
    if len(files) > 5:
        print(f"   ... and {len(files) - 5} more files")
    
    # Test with limited files
    max_files = min(2, len(files))  # Process max 2 files for demo
    print(f"\n🚀 Processing {max_files} files for demonstration...")
    
    try:
        # Process files
        stats = await processor.process_files(test_folder, max_files, 999)  # Use run number 999 for demo
        
        print(f"\n✅ Demo completed successfully!")
        print(f"   Processed: {stats['processed']} files")
        print(f"   Failed: {stats['failed']} files")
        print(f"   Duration: {stats['duration_seconds']:.2f} seconds")
        
        # Show output files
        output_dir = Path("output")
        demo_files = list(output_dir.glob("run999_*.json"))
        if demo_files:
            print(f"\n📄 Generated output files:")
            for file_path in demo_files:
                print(f"   {file_path}")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")


def show_usage_examples():
    """Show usage examples for the test_classification.py script."""
    
    print("\n📖 Usage Examples:")
    print("=" * 50)
    
    script_path = "scripts/test_classification.py"
    
    examples = [
        {
            "description": "Process all files in a folder",
            "command": f"python {script_path} /path/to/folder"
        },
        {
            "description": "Process maximum 10 files",
            "command": f"python {script_path} /path/to/folder --max-files 10"
        },
        {
            "description": "Specify custom output directory and run number",
            "command": f"python {script_path} /path/to/folder --output-dir results --run-number 2"
        },
        {
            "description": "Process BOL documents with run number 1",
            "command": f"python {script_path} input_data/1_bol --max-files 5 --run-number 1"
        },
        {
            "description": "Process fuel receipts with custom output",
            "command": f"python {script_path} input_data/9_fuel_receipt --output-dir fuel_results --run-number 3"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}:")
        print(f"   {example['command']}")
    
    print(f"\n💡 Tips:")
    print(f"   • Supported file types: PDF, JPG, JPEG, PNG, TIF, TIFF")
    print(f"   • Files are processed recursively from subfolders")
    print(f"   • Output files are named: run<N>_<original_filename>.json")
    print(f"   • Processing statistics are saved as: run<N>_stats.json")
    print(f"   • Temp files in S3 are automatically deleted after processing")


async def main():
    """Main function for the demo."""
    
    print("🎯 Test Classification Demo")
    print("This demo shows how to use the classification test script.")
    
    # Show usage examples
    show_usage_examples()
    
    # Ask user if they want to run the demo
    print(f"\n❓ Do you want to run a demo classification? (y/n): ", end="")
    
    # For automated testing, we'll skip the interactive part
    # In a real scenario, you could uncomment the following lines:
    # response = input().strip().lower()
    # if response in ['y', 'yes']:
    #     await demo_classification()
    # else:
    #     print("Demo skipped.")
    
    # For now, just show that the demo is available
    print("Demo available - uncomment interactive code to run")
    
    print(f"\n🎉 Demo script completed!")


if __name__ == "__main__":
    asyncio.run(main())
