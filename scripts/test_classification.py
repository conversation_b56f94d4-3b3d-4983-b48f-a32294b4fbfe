#!/usr/bin/env python3
"""
Test Classification Script

This script:
- Uploads files to S3 bucket inside temp folder
- Gives S3 URI to main.py function to classify
- Deletes temp files after classification
- Uses AWS credentials from .env file
- Uses main.py file to get results
- Stores whole result in output folder with serial number
- Classifies all files inside given folder and its subfolders
- Takes argument for max number of files to process
- Uses async to process faster
- Output JSON files have same name as input files
"""

import sys
import os
import asyncio
import json
import argparse
import uuid
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
import boto3
from botocore.exceptions import ClientError
import dotenv
from datetime import datetime

# Add the main script path to sys.path
sys.path.append(r"/home/<USER>/Documents/repositories/document-data-extraction")
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
dotenv.load_dotenv()

# Import the main classification function
try:
    # Try importing from the external repository first
    sys.path.insert(0, r"/home/<USER>/Documents/repositories/document-data-extraction")
    from app.llm.classification import main as llm_classification
except ImportError:
    try:
        # Fallback to local scripts directory
        from scripts.main import main as llm_classification
    except ImportError:
        # Last resort - try direct import
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", os.path.join(os.path.dirname(__file__), "main.py"))
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        llm_classification = main_module.main


class S3Manager:
    """Manages S3 operations for file upload and deletion."""
    
    def __init__(self):
        """Initialize S3 client with credentials from environment."""
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_DEFAULT_REGION', 'us-east-1')
        )
        self.bucket_name = os.getenv('S3_BUCKET_NAME', 'document-extraction-logistically')
        self.temp_prefix = 'temp'
    
    async def upload_file(self, local_file_path: str) -> str:
        """
        Upload a file to S3 temp folder.
        
        Args:
            local_file_path: Path to the local file
            
        Returns:
            S3 URI of the uploaded file
        """
        file_name = Path(local_file_path).name
        # Generate unique filename to avoid conflicts
        unique_id = str(uuid.uuid4())[:8]
        s3_key = f"{self.temp_prefix}/{unique_id}_{file_name}"
        
        try:
            # Upload file to S3
            self.s3_client.upload_file(local_file_path, self.bucket_name, s3_key)
            s3_uri = f"s3://{self.bucket_name}/{s3_key}"
            print(f"✓ Uploaded: {local_file_path} -> {s3_uri}")
            return s3_uri
        except ClientError as e:
            print(f"✗ Failed to upload {local_file_path}: {e}")
            raise
    
    async def delete_file(self, s3_uri: str) -> bool:
        """
        Delete a file from S3.
        
        Args:
            s3_uri: S3 URI of the file to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Parse S3 URI
            if not s3_uri.startswith('s3://'):
                raise ValueError(f"Invalid S3 URI: {s3_uri}")
            
            # Extract bucket and key from URI
            uri_parts = s3_uri[5:].split('/', 1)  # Remove 's3://' and split
            bucket = uri_parts[0]
            key = uri_parts[1] if len(uri_parts) > 1 else ''
            
            # Only delete files from temp folder for safety
            if not key.startswith(self.temp_prefix):
                print(f"⚠ Skipping deletion - file not in temp folder: {s3_uri}")
                return False
            
            # Delete the file
            self.s3_client.delete_object(Bucket=bucket, Key=key)
            print(f"✓ Deleted: {s3_uri}")
            return True
        except ClientError as e:
            print(f"✗ Failed to delete {s3_uri}: {e}")
            return False


class FileProcessor:
    """Processes files for classification."""
    
    def __init__(self, output_dir: str = "output"):
        """Initialize file processor."""
        self.s3_manager = S3Manager()
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.processed_count = 0
    
    def get_supported_files(self, folder_path: str) -> List[str]:
        """
        Get all supported files from folder and subfolders.
        
        Args:
            folder_path: Path to the folder to scan
            
        Returns:
            List of file paths
        """
        supported_extensions = {'.pdf', '.jpg', '.jpeg', '.png', '.tif', '.tiff'}
        files = []
        
        folder = Path(folder_path)
        if not folder.exists():
            raise ValueError(f"Folder does not exist: {folder_path}")
        
        # Recursively find all supported files
        for file_path in folder.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                files.append(str(file_path))
        
        return sorted(files)
    
    async def process_single_file(self, file_path: str, run_number: int) -> Tuple[bool, str, str, Dict]:
        """
        Process a single file through the classification pipeline.

        Args:
            file_path: Path to the local file
            run_number: Run number for output file naming

        Returns:
            Tuple of (success, error_message, filename, classification_result)
        """
        s3_uri = None
        filename = Path(file_path).name
        classification_result = {}

        try:
            # Upload file to S3
            start_time = datetime.now()
            print(f"⬆️ [{start_time.strftime('%H:%M:%S')}] Uploading: {filename}")
            s3_uri = await self.s3_manager.upload_file(file_path)

            # Classify using main.py function
            classify_time = datetime.now()
            print(f"🔍 [{classify_time.strftime('%H:%M:%S')}] Classifying: {filename}")
            result = await llm_classification(s3_uri)

            # Extract classification result for terminal output
            classification_result = result.get("classification_result", {})

            # Generate output filename with same name as input file
            input_filename = Path(file_path).stem  # filename without extension
            output_filename = f"run{run_number}_{input_filename}.json"
            output_path = self.output_dir / output_filename

            # Save result to output folder
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=4, ensure_ascii=False, default=str)

            # Print filename and classification result
            print(f"📄 File: {filename}")
            print(f"📋 Classification: {json.dumps(classification_result, indent=2)}")
            print(f"✓ Saved result: {output_path}")
            self.processed_count += 1

            return True, "", filename, classification_result

        except Exception as e:
            error_msg = f"Failed to process {file_path}: {str(e)}"
            print(f"✗ {error_msg}")
            return False, error_msg, filename, classification_result

        finally:
            # Always try to delete the temp file from S3
            if s3_uri:
                await self.s3_manager.delete_file(s3_uri)
    
    async def process_files(self, folder_path: str, max_files: Optional[int] = None, run_number: int = 1) -> Dict:
        """
        Process all files in the folder with async processing.
        
        Args:
            folder_path: Path to the folder containing files
            max_files: Maximum number of files to process (None for all)
            run_number: Run number for output file naming
            
        Returns:
            Dictionary with processing statistics
        """
        # Get all supported files
        files = self.get_supported_files(folder_path)
        
        if not files:
            print(f"⚠ No supported files found in {folder_path}")
            return {"total_files": 0, "processed": 0, "failed": 0, "errors": []}
        
        # Limit files if max_files is specified
        if max_files and max_files > 0:
            files = files[:max_files]
        
        print(f"📁 Found {len(files)} files to process")
        print(f"🚀 Starting processing with run number: {run_number}")
        
        # Process files with true parallel processing using asyncio.create_task
        print(f"🚀 Processing {len(files)} files in TRUE PARALLEL (all at once)...")

        # Create all tasks at once and start them immediately for true parallel execution
        start_time = datetime.now()
        tasks = []

        # Create and start all tasks immediately
        for file_path in files:
            task = asyncio.create_task(self.process_single_file(file_path, run_number))
            tasks.append(task)

        print(f"🚀 All {len(tasks)} tasks started simultaneously - processing in parallel...")

        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = datetime.now()
        
        # Collect statistics
        successful = 0
        failed = 0
        errors = []
        processed_files = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed += 1
                errors.append(f"File {files[i]}: {str(result)}")
            elif isinstance(result, tuple) and len(result) >= 4:
                if result[0]:  # success
                    successful += 1
                    processed_files.append({
                        "filename": result[2],
                        "classification": result[3]
                    })
                else:  # failure
                    failed += 1
                    errors.append(result[1])
            else:
                failed += 1
                errors.append(f"File {files[i]}: Invalid result format")
        
        # Print summary
        duration = (end_time - start_time).total_seconds()
        print(f"\n📊 Processing Summary:")
        print(f"   Total files: {len(files)}")
        print(f"   Successful: {successful}")
        print(f"   Failed: {failed}")
        print(f"   Duration: {duration:.2f} seconds")
        print(f"   Output directory: {self.output_dir}")

        # Print processed files summary
        if processed_files:
            print(f"\n📋 Successfully Processed Files:")
            for file_info in processed_files:
                print(f"   📄 {file_info['filename']}: {json.dumps(file_info['classification'], separators=(',', ':'))}")

        if errors:
            print(f"\n❌ Errors:")
            for error in errors[:5]:  # Show first 5 errors
                print(f"   {error}")
            if len(errors) > 5:
                print(f"   ... and {len(errors) - 5} more errors")
        
        return {
            "total_files": len(files),
            "processed": successful,
            "failed": failed,
            "errors": errors,
            "duration_seconds": duration
        }


async def main():
    """Main function to run the classification test."""
    parser = argparse.ArgumentParser(description='Test document classification with S3 upload/download')
    parser.add_argument('folder_path', help='Path to folder containing files to classify')
    parser.add_argument('--max-files', type=int, default=None,
                       help='Maximum number of files to process (default: all files)')
    parser.add_argument('--output-dir', default='output',
                       help='Output directory for results (default: output)')
    parser.add_argument('--run-number', type=int, default=1,
                       help='Run number for output file naming (default: 1)')

    args = parser.parse_args()

    # Validate folder path
    if not os.path.exists(args.folder_path):
        print(f"❌ Error: Folder does not exist: {args.folder_path}")
        return 1

    # Initialize processor
    processor = FileProcessor(args.output_dir)

    try:
        # Process files
        stats = await processor.process_files(
            args.folder_path,
            args.max_files,
            args.run_number
        )

        # Save processing statistics
        stats_file = processor.output_dir / f"run{args.run_number}_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=4, ensure_ascii=False, default=str)

        print(f"\n📈 Statistics saved to: {stats_file}")

        return 0 if stats['failed'] == 0 else 1

    except Exception as e:
        print(f"❌ Fatal error: {e}")
        return 1


if __name__ == "__main__":
    # Example usage in script
    if len(sys.argv) == 1:
        # Default test run
        print("🧪 Running test with default parameters...")
        test_folder = "/home/<USER>/Desktop/test_ligistically/input_data"
        max_files = 3

        async def test_run():
            processor = FileProcessor("output")
            stats = await processor.process_files(test_folder, max_files, 1)
            print(f"\n✅ Test completed: {stats}")

        asyncio.run(test_run())
    else:
        # Run with command line arguments
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
