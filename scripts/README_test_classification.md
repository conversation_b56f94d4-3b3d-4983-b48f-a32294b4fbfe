# Test Classification Script

This script (`test_classification.py`) provides a comprehensive testing framework for document classification using S3 upload/download and the main classification pipeline.

## Features

- ✅ **S3 Integration**: Uploads files to S3 temp folder and cleans up after processing
- ✅ **Async Processing**: Uses async/await for faster concurrent processing
- ✅ **Recursive File Discovery**: Processes all files in folder and subfolders
- ✅ **Flexible Output**: Configurable output directory and run numbering
- ✅ **Safety**: Only deletes files from temp folder in S3
- ✅ **Statistics**: Generates processing statistics and error reports
- ✅ **File Naming**: Output files maintain original filename structure

## Requirements

- AWS credentials configured in `.env` file
- Access to the document classification pipeline
- Python packages: `boto3`, `asyncio`, `pathlib`

## Usage

### Basic Usage

```bash
# Process all files in a folder
python scripts/test_classification.py /path/to/folder

# Process with maximum file limit
python scripts/test_classification.py input_data/1_bol --max-files 5

# Custom output directory and run number
python scripts/test_classification.py input_data/9_fuel_receipt --output-dir results --run-number 3
```

### Command Line Arguments

- `folder_path` (required): Path to folder containing files to classify
- `--max-files`: Maximum number of files to process (default: all files)
- `--output-dir`: Output directory for results (default: output)
- `--run-number`: Run number for output file naming (default: 1)

### Supported File Types

- PDF (.pdf)
- Images: JPG, JPEG, PNG, TIF, TIFF

## Output Structure

### Result Files
- **Format**: `run{N}_{original_filename}.json`
- **Content**: Complete classification result including:
  - Textract responses
  - Bedrock responses  
  - Classification results
  - Combined text

### Statistics File
- **Format**: `run{N}_stats.json`
- **Content**: Processing statistics including:
  - Total files processed
  - Success/failure counts
  - Processing duration
  - Error details

## Examples

### Example 1: Process BOL Documents
```bash
python scripts/test_classification.py input_data/1_bol --max-files 10 --run-number 1
```

### Example 2: Process Fuel Receipts
```bash
python scripts/test_classification.py input_data/9_fuel_receipt --output-dir fuel_results --run-number 2
```

### Example 3: Process All Invoice Documents
```bash
python scripts/test_classification.py input_data/10_invoice --run-number 3
```

## Configuration

The script uses environment variables from `.env` file:
- `AWS_ACCESS_KEY_ID`: AWS access key
- `AWS_SECRET_ACCESS_KEY`: AWS secret key
- `AWS_DEFAULT_REGION`: AWS region (default: us-east-1)
- `S3_BUCKET_NAME`: S3 bucket name (default: document-extraction-logistically)

## Safety Features

1. **Temp File Management**: Only uploads to and deletes from S3 temp folder
2. **Error Handling**: Graceful error handling with detailed error reporting
3. **Concurrent Limits**: Limits concurrent operations to avoid overwhelming the system
4. **Cleanup**: Always attempts to delete temp files even if processing fails

## Sample Output

```
📁 Found 3 files to process
🚀 Starting processing with run number: 1
✓ Uploaded: input_data/1_bol/1_bol_1.pdf -> s3://bucket/temp/uuid_1_bol_1.pdf
🔍 Classifying: 1_bol_1.pdf
✓ Saved result: output/run1_1_bol_1.json
✓ Deleted: s3://bucket/temp/uuid_1_bol_1.pdf

📊 Processing Summary:
   Total files: 3
   Successful: 3
   Failed: 0
   Duration: 45.23 seconds
   Output directory: output
```

## Testing

A demo script is available in `Augment_test/test_classification_demo.py` that shows usage examples and can run test classifications.

```bash
python Augment_test/test_classification_demo.py
```

## Troubleshooting

1. **Import Errors**: Ensure the document-data-extraction repository path is correct
2. **AWS Errors**: Verify AWS credentials and S3 bucket access
3. **File Not Found**: Check that input folder exists and contains supported files
4. **Permission Errors**: Ensure write permissions for output directory
