import sys
sys.path.append(r"/home/<USER>/Documents/repositories/document-data-extraction")

import dotenv
from app.llm.classification import main as llm_classification


dotenv.load_dotenv()
import asyncio
import json

file_path = "s3://document-extraction-logistically/temp/11360615_fuel_receipt_UKFZSKSF89HAX5QFYPM8.jpg"
result = asyncio.run(llm_classification(file_path))
print(json.dumps(result["classification_result"], indent=4))
